import { Binary, Expression, Grouping, Literal, Unary } from "./Expression.js";
import { Lox } from "./Lox.js";
import { Token, TokenType } from "./Token.js";

/**
 * @example
 * expression     → equality ;
 * equality       → comparison ( ( "!=" | "==" ) comparison )* ;
 * comparison     → term ( ( ">" | ">=" | "<" | "<=" ) term )* ;
 * term           → factor ( ( "-" | "+" ) factor )* ;
 * factor         → unary ( ( "/" | "*" ) unary )* ;
 * unary          → ( "!" | "-" ) unary
 *                | primary ;
 * primary        → NUMBER | STRING | "true" | "false" | "nil"
 *                | "(" expression ")" ;
 */
export class Parser {
	private tokens: Token[] = [];
	private current = 0;

	constructor(tokens: Token[]) {
		this.tokens = tokens;
	}

	parse(): Expression {
		return this.expression();
	}

	private expression(): Expression {
		return this.equality();
	}

	private equality(): Expression {
		let expr = this.comparison();

		while (this.match(["==", "!="])) {
			const operator = this.previous();
			const right = this.comparison();
			expr = new Binary(expr, operator, right);
		}

		return expr;
	}

	private comparison(): Expression {
		let expr = this.term();

		while (this.match(["<", "<=", ">", ">="])) {
			const operator = this.previous();
			const right = this.term();
			expr = new Binary(expr, operator, right);
		}

		return expr;
	}

	private term(): Expression {
		let expr = this.factor();

		while (this.match(["-", "+"])) {
			const operator = this.previous();
			const right = this.factor();
			expr = new Binary(expr, operator, right);
		}

		return expr;
	}

	private factor(): Expression {
		let expr = this.unary();

		while (this.match(["/", "*"])) {
			const operator = this.previous();
			const right = this.unary();
			expr = new Binary(expr, operator, right);
		}

		return expr;
	}

	private unary(): Expression {
		if (this.match(["!", "-"])) {
			const operator = this.previous();
			const right = this.unary();
			return new Unary(operator, right);
		}

		return this.primary();
	}

	private primary(): Expression {
		if (this.match(["number", "string"])) {
			return new Literal(this.previous().literal);
		}

		if (this.match(["false"])) {
			return new Literal(false);
		}
		if (this.match(["true"])) {
			return new Literal(true);
		}
		if (this.match(["nil"])) {
			return new Literal(null);
		}

		if (this.match(["("])) {
			const expr = this.expression();
			this.consume(")", "Expected ')' after expression.");
			return new Grouping(expr);
		}

		throw new Error("Expected expression.");
	}

	// Parsing infrastructure

	private match(types: TokenType[]): boolean {
		for (const type of types) {
			if (this.check(type)) {
				this.advance();
				return true;
			}
		}

		return false;
	}

	private check(type: TokenType): boolean {
		if (this.isAtEnd()) {
			return false;
		}
		return this.peek().type === type;
	}

	private advance(): Token {
		if (!this.isAtEnd()) {
			this.current++;
		}
		return this.previous();
	}

	private peek(): Token {
		return this.tokens[this.current];
	}

  private consume(type: TokenType, message: string) {
    if(this.check(type)) {
      return this.advance()
    }

    throw new ParseError(this.peek(), message);
   }

	private previous(): Token {
		return this.tokens[this.current - 1];
	}

	private isAtEnd(): boolean {
		return this.peek().type === "EOF";
	}
}

class ParseError extends Error {
	constructor(public token: Token, message: string) {
    Lox.parseError(token, message)
    // Lox.error(token.line, message);
		super(message);
	}
}
