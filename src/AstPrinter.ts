import { Binary, Expression, Grouping, Literal, Unary } from "./Expression.js";
import { Visitor } from "./Visitor.js";

export class AstPrinter implements Visitor<string> {
	print(expression: Expression): string {
		return expression.accept(this);
	}

	visitBinaryExpression(binary: Binary): string {
		return this.parenthesize(binary.operator.lexeme, [
			binary.left,
			binary.right,
		]);
	}

	visitGroupingExpression(grouping: Grouping): string {
		return this.parenthesize("group", [grouping.expression]);
	}

	visitLiteralExpression(literal: Literal): string {
		if (literal.value === null) {
			return "nil";
		}
		return String(literal.value);
	}

	visitUnaryExpression(unary: Unary): string {
		return this.parenthesize(unary.operator.lexeme, [unary.right]);
	}

	private parenthesize(name: string, expressions: Expression[]): string {
		const expressionsString = expressions
			.map((expr) => expr.accept(this))
			.join(" ");
		return `(${name} ${expressionsString})`;
	}
}
