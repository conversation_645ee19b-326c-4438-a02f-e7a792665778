import { Lox } from "./Lox.js";
import { isKeyword, Literal, Token, TokenType } from "./Token.js";

export class Scanner {
	private tokens: Token[] = [];
	private start = 0;
	private current = 0;
	private line = 1;

	constructor(private source: string) {}

	scanTokens(): Token[] {
		while (!this.isAtEnd()) {
			this.start = this.current;
			this.scanToken();
		}

		this.tokens.push(new Token("EOF", "", null, this.line));
		return this.tokens;
	}

	private scanToken() {
		const nextCharacter = this.advance();
		switch (nextCharacter) {
			case "(":
				this.addToken("(");
				break;
			case ")":
				this.addToken(")");
				break;
			case "{":
				this.addToken("{");
				break;
			case "}":
				this.addToken("}");
				break;
			case ",":
				this.addToken(",");
				break;
			case ".":
				this.addToken(".");
				break;
			case "-":
				this.addToken("-");
				break;
			case "+":
				this.addToken("+");
				break;
			case ";":
				this.addToken(";");
				break;
			case "*":
				this.addToken("*");
				break;
			case "!":
				this.addToken(this.nextCharMatches("=") ? "!=" : "!");
				break;
			case "=":
				this.addToken(this.nextCharMatches("=") ? "==" : "=");
				break;
			case "<":
				this.addToken(this.nextCharMatches("=") ? "<=" : "<");
				break;
			case ">":
				this.addToken(this.nextCharMatches("=") ? ">=" : ">");
				break;
			case "/":
				if (this.nextCharMatches("/")) {
					// Ignore comments until end of line
					while (this.peek() !== "\n" && !this.isAtEnd()) {
						this.advance();
					}
				} else {
					this.addToken("/");
				}
				break;
			case " ":
			case "\t":
			case "\r":
				// Ignore whitespaces
				break;
			case "\n":
				this.line++;
				break;
			case '"':
				this.string();
				break;
			default:
				if (this.isDigit(nextCharacter)) {
					this.number();
					return;
				}
				if (this.isAlpha(nextCharacter)) {
					this.identifier();
					return;
				}
				Lox.error(this.line, "Unexpected character");
				break;
		}
	}

	private addToken(type: TokenType) {
		this.addTokenWithLiteral(type, null);
	}

	private addTokenWithLiteral(type: TokenType, literal: Literal) {
		const text = this.source.substring(this.start, this.current);
		this.tokens.push(new Token(type, text, literal, this.line));
	}

	private string() {
		while (this.peek() !== '"' && !this.isAtEnd()) {
			if (this.peek() === "\n") {
				this.line++;
			}
			this.advance();
		}

		if (this.isAtEnd()) {
			Lox.error(this.line, "Unterminated string");
			return;
		}

		// The closing "
		this.advance();

		const value = this.source.substring(this.start + 1, this.current - 1);
		this.addTokenWithLiteral("string", value);
	}

	private number() {
		while (this.isDigit(this.peek())) {
			this.advance();
		}

		// Look for a fractional part
		if (this.peek() === "." && this.isDigit(this.peekNext())) {
			// Consume the "."
			this.advance();

			while (this.isDigit(this.peek())) {
				this.advance();
			}
		}

		this.addTokenWithLiteral(
			"number",
			Number(this.source.substring(this.start, this.current)),
		);
	}

	private identifier() {
		while (this.isAlphaNumeric(this.peek())) {
			this.advance();
		}

		const text = this.source.substring(this.start, this.current);
		const type = isKeyword(text) ? text : "identifier";

		this.addToken(type);
	}

	private isDigit(c: string) {
		return c >= "0" && c <= "9";
	}

	private isAlpha(c: string) {
		return (c >= "a" && c <= "z") || (c >= "A" && c <= "Z") || c === "_";
	}

	private isAlphaNumeric(c: string) {
		return this.isAlpha(c) || this.isDigit(c);
	}

	private nextCharMatches(expected: string) {
		if (this.isAtEnd()) {
			return false;
		}
		if (this.peek() !== expected) {
			return false;
		}

		this.current++;
		return true;
	}

	private advance() {
		return this.source.charAt(this.current++);
	}

	private peek() {
		if (this.isAtEnd()) {
			return "\0";
		}
		return this.source.charAt(this.current);
	}

	private peekNext() {
		if (this.current + 1 >= this.source.length) {
			return "\0";
		}
		return this.source.charAt(this.current + 1);
	}

	private isAtEnd() {
		return this.current >= this.source.length;
	}
}
