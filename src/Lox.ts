import fs from "node:fs";
import { <PERSON><PERSON><PERSON> } from "./Scanner.js";
import { Token } from "./Token.js";

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export class Lox {
	static hadError = false;

	constructor(args: string[]) {
		if (args.length > 1) {
			console.log("Usage: jlox [script]");
			process.exit(64);
		} else if (args.length == 1) {
			Lox.runFile(args[0]);
		} else {
			Lox.runPrompt();
		}
	}

	private static runFile(path: string): void {
		const buffer = fs.readFileSync(path);
		Lox.run(buffer.toString("utf-8"));
		if (Lox.hadError) {
			process.exit(65);
		}
	}

	private static runPrompt(): void {
		process.stdin.setEncoding("utf8");
		process.stdin.on("data", (data) => {
			Lox.run(data.toString("utf-8"));
			Lox.hadError = false;
		});
	}
	private static run(source: string): void {
		const scanner = new Scanner(source);
		const tokens = scanner.scanTokens();
		// For now, just print the tokens.
		for (const token of tokens) {
			console.log(token);
		}
	}

	static parseError(token: Token, message: string): void {
		if (token.type === "EOF") {
			Lox.report(token.line, " at end", message);
		} else {
			Lox.report(token.line, ` at '${token.lexeme}'`, message);
		}
	}

	static error(line: number, message: string): void {
		Lox.report(line, "", message);
	}

	private static report(line: number, where: string, message: string): void {
		console.error(`[line ${String(line)}] Error${where}: ${message}`);
		Lox.hadError = true;
	}
}
