import { Token } from "./Token.js";
import { Visitor } from "./Visitor.js";

export class Binary implements Expression {
	constructor(
		public left: Expression,
		public operator: Token,
		public right: Expression,
	) {}

	accept<T>(visitor: Visitor<T>): T {
		return visitor.visitBinaryExpression(this);
	}
}

export class Grouping implements Expression {
	constructor(public expression: Expression) {}

	accept<T>(visitor: Visitor<T>): T {
		return visitor.visitGroupingExpression(this);
	}
}

export class Literal implements Expression {
	constructor(public value: boolean | string | number | null) {}

	accept<T>(visitor: Visitor<T>): T {
		return visitor.visitLiteralExpression(this);
	}
}

export class Unary implements Expression {
	constructor(
		public operator: Token,
		public right: Expression,
	) {}

	accept<T>(visitor: Visitor<T>): T {
		return visitor.visitUnaryExpression(this);
	}
}

/**
 * @example
 * expression     → literal
 *                | unary
 *                | binary
 *                | grouping ;
 *
 * literal        → NUMBER | STRING | "true" | "false" | "nil" ;
 * grouping       → "(" expression ")" ;
 * unary          → ( "-" | "!" ) expression ;
 * binary         → expression operator expression ;
 * operator       → "==" | "!=" | "<" | "<=" | ">" | ">="
 *                | "+"  | "-"  | "*" | "/" ;
 */
export abstract class Expression {
	abstract accept<T>(visitor: Visitor<T>): T;
}
