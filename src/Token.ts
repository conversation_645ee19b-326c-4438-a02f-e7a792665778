export type TokenType =
	| SingleCharacterToken
	| OneOrTwoCharacterToken
	| LiteralToken
	| KeywordToken
	| EOFToken;

export type SingleCharacterToken =
	| "("
	| ")"
	| "{"
	| "}"
	| ","
	| "."
	| "-"
	| "+"
	| ";"
	| "/"
	| "*";
export type OneOrTwoCharacterToken =
	| "!"
	| "!="
	| "="
	| "=="
	| ">"
	| ">="
	| "<"
	| "<=";
export type LiteralToken = "identifier" | "string" | "number";

export const KEYWORDS = [
	"and",
	"class",
	"else",
	"false",
	"fun",
	"for",
	"if",
	"nil",
	"or",
	"print",
	"return",
	"super",
	"this",
	"true",
	"var",
	"while",
] as const;
export type KeywordToken = (typeof KEYWORDS)[number];
export function isKeyword(word: string): word is KeywordToken {
	return KEYWORDS.includes(word as KeywordToken);
}

export type EOFToken = "EOF";

export type Literal = string | number | null;

export class Token {
	constructor(
		public type: TokenType,
		public lexeme: string,
		public literal: Literal,
		public line: number,
	) {}

	public toString(): string {
		return `${this.type} ${this.lexeme} ${String(this.literal)}`;
	}
}
